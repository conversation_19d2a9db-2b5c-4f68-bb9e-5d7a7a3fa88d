<%- contentFor('title') %>Team Management<%- contentFor('title') %>

<style>
.teams-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-header h1 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.team-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.team-card:hover {
  transform: translateY(-3px);
}

.team-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
}

.team-name {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0;
}

.member-count {
  background: #3498db;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.team-description {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.team-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

.loading {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 2rem;
}

.error {
  color: #e74c3c;
  background: #fdf2f2;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
  margin: 1rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  margin-bottom: 1rem;
  font-weight: 600;
}

.back-link:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .teams-container {
    padding: 1rem;
  }

  .teams-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<div class="teams-container">
  <a href="/manager" class="back-link">
    ← Back to Manager Dashboard
  </a>

  <div class="page-header">
    <h1>Team Management</h1>
    <p>Manage your teams and team members</p>
  </div>

  <div id="teamsContainer" class="loading">
    Loading teams...
  </div>
</div>

<script>
// Team management functionality
let backendApiUrl = 'http://localhost:3445';

document.addEventListener('DOMContentLoaded', function() {
    loadTeams();
});

function getJWTToken() {
    return localStorage.getItem('jwtToken') || sessionStorage.getItem('jwtToken');
}

async function ensureAuthentication() {
    const token = getJWTToken();
    if (!token) {
        try {
            const response = await fetch('/api/auth/token', {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    return true;
                }
            }
        } catch (error) {
            console.error('Failed to get JWT token:', error);
        }
        return false;
    }
    return true;
}

async function loadTeams() {
    if (!(await ensureAuthentication())) {
        document.getElementById('teamsContainer').innerHTML = '<div class="error">Authentication failed. Please log in again.</div>';
        return;
    }

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/teams`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                displayTeams(data.teams);
            } else {
                document.getElementById('teamsContainer').innerHTML = '<div class="error">Failed to load teams: ' + data.message + '</div>';
            }
        } else {
            document.getElementById('teamsContainer').innerHTML = '<div class="error">Failed to load teams. Status: ' + response.status + '</div>';
        }
    } catch (error) {
        console.error('Error loading teams:', error);
        document.getElementById('teamsContainer').innerHTML = '<div class="error">Error loading teams: ' + error.message + '</div>';
    }
}

function displayTeams(teams) {
    const container = document.getElementById('teamsContainer');
    
    if (teams.length === 0) {
        container.innerHTML = '<div class="loading">No teams found.</div>';
        return;
    }

    const teamsHtml = teams.map(team => `
        <div class="team-card">
            <div class="team-header">
                <h3 class="team-name">${team.team_name}</h3>
                <span class="member-count">${team.member_count} members</span>
            </div>
            <p class="team-description">${team.description || 'No description available'}</p>
            <div class="team-actions">
                <button class="btn btn-primary" onclick="viewTeamMembers(${team.team_id})">
                    View Members
                </button>
                <button class="btn btn-secondary" onclick="manageTeam(${team.team_id})">
                    Manage
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = `<div class="teams-grid">${teamsHtml}</div>`;
}

async function viewTeamMembers(teamId) {
    // This would open a modal or navigate to a detailed view
    alert('Team members view - to be implemented');
}

async function manageTeam(teamId) {
    // This would open team management interface
    alert('Team management - to be implemented');
}
</script>
